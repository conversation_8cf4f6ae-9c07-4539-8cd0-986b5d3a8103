import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';

enum Api {
  getAgentList = '/bdm/agent/page',
  addAgent = '/bdm/agent/add',
  editAgent = '/bdm/agent/update',
  deleteAgent = '/bdm/agent/delete',
  info = '/bdm/agent/info',
  getProvince = '/magic-api/common/getProvince',
  getCityByProvinceCode = '/magic-api/common/getCityByProvinceCode',
  dictionaryDetail = '/system/dictionary-detail',
  getProductSelect = '/base/product/getProductSelect',
  getCode = '/system/code-rule/generate',
}

/**
 * @description: 打卡记录列表(分页)
 */
export async function getAgentList(params?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.getAgentList,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
export async function getCode(params?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.getCode,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
export async function getAgentDetail(params?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.info,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function dictionaryDetail(params?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.dictionaryDetail,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function getProductSelect(params?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.getProductSelect,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
export async function getProvince(params?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.getProvince,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
export async function getCityByProvinceCode(params?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.getCityByProvinceCode,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 签到设置
 */
export async function addAgent(data?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.addAgent,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 根据id查询BdmAgent信息
 */
export async function editAgent(data?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.editAgent,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除
 */
export async function deleteAgent(data?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.deleteAgent,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}
